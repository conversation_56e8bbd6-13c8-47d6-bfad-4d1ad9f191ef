name: "Docker Build and Push"
description: "Builds and pushes a Docker image"

inputs:
  docker-image-name:
    description: "Name of the Docker image"
    required: true
  short-commit-hash:
    description: "Short commit hash for tagging"
    required: true
  dockerhub-username:
    description: "DockerHub username"
    required: true
  dockerhub-token:
    description: "DockerHub token"
    required: true
  github-server-url:
    description: "GitHub server URL"
    required: true
  github-repository:
    description: "GitHub repository"
    required: true
  github-sha:
    description: "Full GitHub SHA"
    required: true

runs:
  using: "composite"
  steps:
    - name: Login to DockerHub
      uses: docker/login-action@v3
      with:
        username: ${{ inputs.dockerhub-username }}
        password: ${{ inputs.dockerhub-token }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Determine if ddtrace should be disabled
      id: check_repo
      shell: bash
      run: |
        if [[ "${{ inputs.github-repository }}" == *"nxt-sh-core"* ]]; then
          echo "disable_ddtrace=true" >> $GITHUB_OUTPUT
        else
          echo "disable_ddtrace=false" >> $GITHUB_OUTPUT
        fi

    - name: Build and push
      id: docker_build
      uses: docker/build-push-action@v6
      with:
        push: true
        tags: ${{ inputs.docker-image-name }}:${{ inputs.short-commit-hash }}
        build-args: |
          DD_GIT_REPOSITORY_URL=${{ inputs.github-server-url }}/${{ inputs.github-repository }}
          DD_GIT_COMMIT_SHA=${{ inputs.github-sha }}
          DISABLE_DDTRACE=${{ steps.check_repo.outputs.disable_ddtrace }}
        labels: |
          com.datadog.git.repository_url=${{ inputs.github-server-url }}/${{ inputs.github-repository }}
          com.datadog.git.commit_sha=${{ inputs.github-sha }}

    - name: Image digest
      shell: bash
      run: echo ${{ steps.docker_build.outputs.digest }}
