name: "Get Slack User Group Mention"
description: "Returns the appropriate user group mention code based on repository name"

inputs:
  repository-name:
    description: "Name of the repository"
    required: true

outputs:
  mention:
    description: "The Slack group mention code"
    value: ${{ steps.read-mappings.outputs.mention }}

runs:
  using: "composite"
  steps:
    - name: Read group mappings
      id: read-mappings
      shell: python
      run: |
        import yaml
        import os

        # Log the environment and inputs
        print("Debug: Starting group mention resolution")
        # GitHub Actions converts dashes to underscores in input names
        repo_name = "${{ inputs.repository-name }}"
        print(f"Debug: Repository name input: '{repo_name}'")
        print(f"Debug: GITHUB_WORKSPACE: {os.environ.get('GITHUB_WORKSPACE', 'not set')}")

        # Construct and verify mappings path
        mappings_path = os.path.join(os.environ['GITHUB_WORKSPACE'], '.github/workflows/.github/actions/get-repo-owner/repo-owner-map.yml')
        print(f"Debug: Looking for mappings file at: {mappings_path}")
        print(f"Debug: File exists: {os.path.exists(mappings_path)}")

        try:
            # Read the mappings file
            with open(mappings_path, 'r') as f:
                print("Debug: Successfully opened mappings file")
                config = yaml.safe_load(f)
                print("Debug: Loaded YAML configuration:")
                print(f"Debug: Available mappings: {list(config.keys())}")

            print(f"Debug: Looking up mention for repository: '{repo_name}'")

            repo_config = config.get(repo_name, config.get('default', {'slack_mention': '', 'email': ''}))
            group_mention = repo_config.get('slack_mention', '')
            print(f"Debug: Resolved group mention: '{group_mention}'")

            # Set the output
            github_output = os.environ.get('GITHUB_OUTPUT')
            if github_output:
                with open(github_output, 'a') as f:
                    output_line = f'mention={group_mention}'
                    print(f"Debug: Writing to GITHUB_OUTPUT: '{output_line}'")
                    f.write(f"{output_line}\n")
            else:
                print("Error: GITHUB_OUTPUT environment variable not set")

            print("Debug: Group mention resolution completed successfully")

        except Exception as e:
            print(f"Error: An exception occurred: {str(e)}")
            print(f"Error: Exception type: {type(e)}")
            raise
