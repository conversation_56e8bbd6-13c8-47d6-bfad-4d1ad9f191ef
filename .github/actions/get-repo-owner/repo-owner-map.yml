sensehawk/sample-vue-js:
  slack_mention: "<@UCMAUFD8A>"
  email: ""
sensehawk/sample-flask:
  slack_mention: "<@UCMAUFD8A>"
  email: ""
sensehawk/taskmapper-frontend:
  slack_mention: "<@UCMAUFD8A>"
  email: ""
sensehawk/core-backend:
  slack_mention: "<@U8RNW2WMV>"
  email: ""
sensehawk/processing-backend:
  slack_mention: "<@U8RNW2WMV>"
  email: ""
sensehawk/forms-backend:
  slack_mention: "<@U018X0NGHD3>"
  email: ""
sensehawk/annotations-backend:
  slack_mention: "<@U018X0NGHD3>"
  email: ""
sensehawk/inventory-backend:
  slack_mention: "<@U03RNSNSJ5B>"
  email: ""
sensehawk/terra-backend:
  slack_mention: "<@U03RNSNSJ5B>"
  email: ""
sensehawk/system-model-backend:
  slack_mention: "<@U03RNSNSJ5B>"
  email: ""
sensehawk/therm-backend:
  slack_mention: "<@UH39DNNFQ>"
  email: ""
default:
  slack_mention: ""
  email: ""

# reviewers group: <!subteam^S08C84A2M0C>
# {
#   "ajayrsh": "U05GLFY6CF3",
#   "Akshitr-sh": "U043XL3HT45",
#   "Anil-Pujeri": "U01S6HYRSS0",
#   "anubhuti-sh": "UF81WG316",
#   "anuj97541": "U03RNSNSJ5B",
#   "anujkumar05": "U018X0NGHD3",
#   "bharath-sensehawk": "U02TG6D8M8T",
#   "casticjehin": "U03B5ERHDEE",
#   "deepakSenseHawk": "U04HMGVQNF9",
#   "gautham-8": "U04CNK1LK6E",
#   "karthikeyanVue": "U013F5W8607",
#   "kdyadav": "UFEL2LBJ7",
#   "kirnh": "UEJU956LE",
#   "mdaffan01": "U01SUS32NLC",
#   "Mondal10": "U0500R9LR6U",
#   "PardeepBaboria": "U03MZP52HM0",
#   "poornimabyregowda19": "UH39DNNFQ",
#   "prajwalbharadwaj": "U0102D01YH4",
#   "saideeptalari": "U8RMBMUF9",
#   "sh-ravan": "UCMAUFD8A",
#   "somasekharkakarla": "U013LSFLL86",
#   "tebbythomas": "U01169SFAMN",
#   "vijaymadhavan": "U8RNW2WMV",
#   "VinitaPotter": "UG5F56B6X"
# }
