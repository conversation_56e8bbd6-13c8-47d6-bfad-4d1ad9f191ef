name: "AWS Lambda Invoke"
description: "Invokes an AWS Lambda function"

inputs:
  aws-access-key:
    description: "AWS access key"
    required: true
  aws-secret-key:
    description: "AWS secret key"
    required: true
  aws-region:
    description: "AWS region"
    required: true
  function-name:
    description: "Lambda function name"
    required: true
  job-name:
    description: "Job name"
    required: true
  short-commit-hash:
    description: "Short commit hash"
    required: true

runs:
  using: "composite"
  steps:
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v3
      with:
        aws-access-key-id: ${{ inputs.aws-access-key }}
        aws-secret-access-key: ${{ inputs.aws-secret-key }}
        aws-region: ${{ inputs.aws-region }}

    - name: Invoke Lambda function
      id: invoke_lambda_function
      shell: bash
      run: |
        RESPONSE=$(aws lambda invoke \
          --function-name ${{ inputs.function-name }} \
          --cli-binary-format raw-in-base64-out \
          --payload '{"job": "${{ inputs.job-name }}", "hash": "${{ inputs.short-commit-hash }}"}' \
          response.json)

        # Get the response body
        RESPONSE_BODY=$(cat response.json)

        # Parse and format the evaluation details
        EVAL_ID=$(echo $RESPONSE_BODY | jq -r '.body.EvalID')
        STATUS_CODE=$(echo $RESPONSE_BODY | jq -r '.statusCode')
        WARNINGS=$(echo $RESPONSE_BODY | jq -r '.body.Warnings')
        WARNINGS_MSG=$([ -z "$WARNINGS" ] && echo "None" || echo "$WARNINGS")

        # Create a summary of the lambda invocation
        echo "## Lambda Invocation Summary" >> $GITHUB_STEP_SUMMARY
        echo "### Function Details" >> $GITHUB_STEP_SUMMARY
        echo "- **Function:** ${{ inputs.function-name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Job Name:** ${{ inputs.job-name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit:** ${{ inputs.short-commit-hash }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Region:** ${{ inputs.aws-region }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Response Details" >> $GITHUB_STEP_SUMMARY
        echo "- **Status Code:** ${STATUS_CODE}" >> $GITHUB_STEP_SUMMARY
        echo "- **Evaluation ID:** ${EVAL_ID}" >> $GITHUB_STEP_SUMMARY
        echo "- **Warnings:** ${WARNINGS_MSG}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "<details><summary><strong>Raw Response</strong></summary>" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo '```json' >> $GITHUB_STEP_SUMMARY
        echo "$RESPONSE_BODY" | jq '.' >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY
        echo "</details>" >> $GITHUB_STEP_SUMMARY

        # Set the output for other steps to use if needed
        echo "response=$RESPONSE_BODY" >> $GITHUB_OUTPUT
        echo "eval_id=$EVAL_ID" >> $GITHUB_OUTPUT
        echo "status_code=$STATUS_CODE" >> $GITHUB_OUTPUT

    - name: Log output to console
      shell: bash
      run: |
        echo "Status Code: ${{ steps.invoke_lambda_function.outputs.status_code }}"
        echo "Evaluation ID: ${{ steps.invoke_lambda_function.outputs.eval_id }}"
