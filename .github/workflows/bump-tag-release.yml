name: "♻️ | Bump, Tag & Release"
# This workflow handles version bumping, tagging, and release creation for both hotfixes and regular releases.

on:
  workflow_call:

env:
  RELEASE_BRANCH: "release/production"

permissions:
  contents: write

jobs:
  bump-tag-and-release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ env.RELEASE_BRANCH }}
          fetch-depth: 0
          fetch-tags: true
          token: ${{ secrets.MAINTENANCE_PAT }}

      - name: Process version and create release
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.MAINTENANCE_PAT }}
          script: |
            const { execSync } = require('child_process');
            const fs = require('fs');

            // Helper function to execute shell commands
            function exec(command) {
              try {
                return execSync(command, { encoding: 'utf8' }).trim();
              } catch (error) {
                console.log(`Command failed: ${command}`);
                console.log(`Error: ${error.message}`);
                return '';
              }
            }

            // Check if this is a merged PR
            if (!context.payload.pull_request?.merged) {
              console.log('PR was not merged, skipping workflow');
              return;
            }

            const pr = context.payload.pull_request;
            const labels = pr.labels.map(label => label.name);

            // Check if this is a hotfix (for patch version bump)
            const isHotfix = pr.base.ref === process.env.RELEASE_BRANCH && labels.includes('hotfix');

            // Check if this is a regular release
            const isRelease = pr.merged && labels.includes('automation:release');

            if (!isHotfix && !isRelease) {
              console.log('This PR does not match hotfix or release criteria, skipping');
              return;
            }

            // Configure Git
            exec('git config user.name "sensehawk-bot"');
            exec('git config user.email "<EMAIL>"');

            // Fetch all tags
            exec('git fetch --tags');

            let newVersion;

            if (isHotfix) {
              console.log('Processing hotfix - bumping patch version');

              // Get latest tag
              const allTags = exec("git tag -l 'v[0-9]*.[0-9]*.[0-9]*' --sort=-v:refname");
              const latestTag = allTags ? allTags.split('\n')[0] : 'v0.0.0';
              console.log(`Latest tag found: ${latestTag}`);

              // Parse and bump patch version
              let currentVersion = latestTag.replace(/^v/, '');
              if (!/^\d+\.\d+\.\d+$/.test(currentVersion)) {
                currentVersion = '0.0.0';
              }

              const [major, minor, patch] = currentVersion.split('.').map(Number);
              newVersion = `${major}.${minor}.${patch + 1}`;
              console.log(`Bumped version: v${newVersion}`);

            } else if (isRelease) {
              console.log('Processing release - extracting version from labels');

              // Look for version label in format vX.Y.Z
              const versionLabel = labels.find(label => /^v\d+\.\d+\.\d+$/.test(label));

              if (versionLabel) {
                newVersion = versionLabel.substring(1); // Remove 'v' prefix
                console.log(`Found version label: ${versionLabel}, extracted: ${newVersion}`);
              } else {
                // Fallback to date-based version
                const now = new Date();
                newVersion = `${now.getFullYear()}.${String(now.getMonth() + 1).padStart(2, '0')}.${String(now.getDate()).padStart(2, '0')}`;
                console.log(`No version label found, using date-based version: ${newVersion}`);
              }
            }

            const tagName = `v${newVersion}`;

            // Update package.json if it exists
            if (fs.existsSync('package.json')) {
              console.log('Updating package.json version');
              const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
              packageJson.version = newVersion;
              fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2) + '\n');

              exec('git add package.json');
              exec(`git commit -m "chore: bump version to ${tagName} [skip ci]"`);
              exec(`git push origin ${process.env.RELEASE_BRANCH}`);
            }

            // Create and push tag
            console.log(`Creating tag ${tagName}`);
            if (isHotfix) {
              exec(`git tag ${tagName}`);
            } else {
              // For releases, tag the merge commit
              exec(`git tag -a "${tagName}" ${pr.merge_commit_sha} -m "${tagName}"`);
            }
            exec(`git push origin ${tagName}`);

            // Create GitHub Release (only for regular releases, not hotfixes)
            if (isRelease) {
              console.log('Creating GitHub Release');

              const releaseBody = `This release was automatically created from the merge of PR #${pr.number}: ${pr.title}`;

              try {
                const release = await github.rest.repos.createRelease({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  tag_name: tagName,
                  name: tagName,
                  body: releaseBody,
                  draft: false,
                  prerelease: false,
                  generate_release_notes: true
                });

                console.log(`✅ Release created: ${release.data.html_url}`);
              } catch (error) {
                console.error('Failed to create release:', error.message);

                if (error.message?.includes?.('body is too long')) {
                  try {
                    const release = await github.rest.repos.createRelease({
                      owner: context.repo.owner,
                      repo: context.repo.repo,
                      tag_name: tagName,
                      name: tagName,
                      body: releaseBody,
                      draft: false,
                      prerelease: false,
                      generate_release_notes: false
                    });

                    console.log(`✅ Release created using fallback approach: ${release.data.html_url}`);
                  } catch (fallbackError) {
                    console.error('Failed to create release with fallback:', fallbackError.message);
                    throw fallbackError;
                  }
                } else {
                  throw error;
                }
              }
            }

            console.log(`✅ ${isHotfix ? 'Hotfix' : 'Release'} processing completed successfully. Tag: ${tagName}`);
