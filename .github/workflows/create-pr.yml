name: "♻️ | Create Pull Request"
description: "Reusable workflow to create a pull request with customizable properties"

on:
  workflow_call:
    inputs:
      title:
        description: "Pull request title"
        required: true
        type: string
      body:
        description: "Pull request description"
        required: false
        type: string
        default: ""
      head:
        description: "Source branch (the branch containing your changes)"
        required: true
        type: string
      base:
        description: "Target branch (the branch you want to merge into)"
        required: false
        type: string
        default: "main"
      labels:
        description: "Comma-separated list of labels to add to the PR"
        required: false
        type: string
        default: ""
      assignees:
        description: "Comma-separated list of GitHub usernames to assign to the PR"
        required: false
        type: string
        default: ""
      reviewers:
        description: "Comma-separated list of GitHub usernames to request reviews from"
        required: false
        type: string
        default: ""
      team_reviewers:
        description: "Comma-separated list of GitHub team slugs to request reviews from"
        required: false
        type: string
        default: ""
      milestone:
        description: "Milestone number to add to the PR"
        required: false
        type: string
        default: ""
      type:
        description: "Type of the pull request ('release' or 'general')"
        required: false
        type: string
        default: "general"
      auto_merge:
        description: "Enable auto-merge for the PR (true/false)"
        required: false
        type: boolean
        default: false
      merge_method:
        description: "Auto-merge method ('merge', 'squash', or 'rebase')"
        required: false
        type: string
        default: "merge"
    outputs:
      pr_number:
        description: "The number of the created pull request"
        value: ${{ jobs.create_pr.outputs.pr_number }}
      pr_url:
        description: "The URL of the created pull request"
        value: ${{ jobs.create_pr.outputs.pr_url }}

jobs:
  create_pr:
    name: Create Pull Request
    runs-on: ubuntu-latest
    outputs:
      pr_number: ${{ steps.create_pr.outputs.pr_number }}
      pr_url: ${{ steps.create_pr.outputs.pr_url }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Verify branches exist
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.MAINTENANCE_PAT }}
          script: |
            const owner = context.repo.owner;
            const repo = context.repo.repo;
            const headBranch = '${{ inputs.head }}';
            const baseBranch = '${{ inputs.base }}';

            try {
              await github.rest.git.getRef({
                owner,
                repo,
                ref: `heads/${headBranch}`
              });
            } catch (error) {
              core.setFailed(`Source branch ${headBranch} does not exist!`);
              return;
            }

            try {
              await github.rest.git.getRef({
                owner,
                repo,
                ref: `heads/${baseBranch}`
              });
            } catch (error) {
              core.setFailed(`Target branch ${baseBranch} does not exist!`);
              return;
            }

      - name: Create Pull Request
        id: create_pr
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.MAINTENANCE_PAT }}
          script: |
            const owner = context.repo.owner;
            const repo = context.repo.repo;

            let body = '${{ inputs.body }}';
            if ('${{ inputs.type }}' === 'release') {
              body = `## 🤖 Release PR\n\nThis PR will merge '${{ inputs.head }}' into '${{ inputs.base }}'.\n\n### ⚠️ IMPORTANT\n**Use regular merge only** - do not squash or rebase merge this PR to preserve the commit history.`;
            } else if ('${{ inputs.type }}' === 'mergeback') {
              body = `## 🤖 Automatic Mergeback PR\n\nThis PR was created automatically following the merge of a PR with the label <code>automation:mergeback</code> to <code>${'${{ inputs.base }}'}</code> branch. Direct merge was attempted but failed, likely due to conflicts.\n\n### ⚠️ IMPORTANT\n**Use regular merge only** - do not squash or rebase merge this PR to preserve the commit history.`;
            }

            try {
              const createResponse = await github.rest.pulls.create({
                owner,
                repo,
                body,
                title: '${{ inputs.title }}',
                head: '${{ inputs.head }}',
                base: '${{ inputs.base }}'
              });
              const prNumber = createResponse.data.number;
              const prUrl = createResponse.data.html_url;

              core.setOutput('pr_number', prNumber);
              core.setOutput('pr_url', prUrl);

              // Add labels if provided
              const labelsInput = `${{ inputs.labels }}`;

              if (labelsInput) {
                const labels = labelsInput.split(',').map(label => label.trim()).filter(Boolean);

                if ('${{ inputs.type }}' === 'release') {
                  if (!labels.includes('automation:release'))
                    labels.push('automation:release');
                  if (!labels.includes('automation:mergeback'))
                    labels.push('automation:mergeback');
                }

                if (labels.length > 0) {
                  await github.rest.issues.addLabels({
                    owner,
                    repo,
                    issue_number: prNumber,
                    labels
                  });
                }
              }

              // Add assignees if provided
              const assigneesInput = '${{ inputs.assignees }}';

              if (assigneesInput) {
                const assignees = assigneesInput.split(',').map(assignee => assignee.trim()).filter(Boolean);

                if (assignees.length > 0) {
                  await github.rest.issues.addAssignees({
                    owner,
                    repo,
                    issue_number: prNumber,
                    assignees
                  });
                }
              }

              // Add reviewers if provided
              const reviewersInput = '${{ inputs.reviewers }}';
              const teamReviewersInput = '${{ inputs.team_reviewers }}';

              if (reviewersInput || teamReviewersInput) {
                const reviewers = reviewersInput ? reviewersInput.split(',').map(reviewer => reviewer.trim()).filter(Boolean) : [];
                const team_reviewers = teamReviewersInput ? teamReviewersInput.split(',').map(team => team.trim()).filter(Boolean) : [];

                if (reviewers.length > 0 || team_reviewers.length > 0) {
                  await github.rest.pulls.requestReviewers({
                    owner,
                    repo,
                    pull_number: prNumber,
                    reviewers,
                    team_reviewers
                  });
                }
              }

              // Add milestone if provided
              const milestoneInput = '${{ inputs.milestone }}';
              if (milestoneInput) {
                await github.rest.issues.update({
                  owner,
                  repo,
                  issue_number: prNumber,
                  milestone: parseInt(milestoneInput)
                });
              }
            } catch (error) {
              core.setFailed(`Failed to create PR: ${error.message}`);
            }

      - name: Enable auto-merge
        if: ${{ inputs.auto_merge == true }}
        env:
          GH_TOKEN: ${{ secrets.MAINTENANCE_PAT }}
        run: |
          gh pr merge ${{ steps.create_pr.outputs.pr_number }} \
            --auto \
            --${{ inputs.merge_method }} \
            --repo ${{ github.repository }}
