name: "♻️ | Create Release PR"

on:
  workflow_call:
    inputs:
      sprint:
        description: Sprint number
        required: true
        type: string
      folder:
        description: Folder number
        required: true
        type: string
      target:
        description: Target
        required: true
        type: string

jobs:
  prepare-inputs:
    runs-on: ubuntu-latest
    outputs:
      title: ${{ steps.set-values.outputs.title }}
      head: ${{ steps.set-values.outputs.head }}
      base: ${{ steps.set-values.outputs.base }}
      type: ${{ steps.set-values.outputs.type }}
      labels: ${{ steps.set-values.outputs.labels }}
    steps:
      - id: set-values
        run: |
          if [[ "${{ inputs.target }}" == "Production" ]]; then
            echo "title=chore(release): v${{ inputs.folder }}.${{ inputs.sprint }}.0 [Sprint ${{ inputs.sprint }} Deployment]" >> $GITHUB_OUTPUT
            echo "head=release/qa" >> $GITHUB_OUTPUT
            echo "base=release/production" >> $GITHUB_OUTPUT
            echo "type=production" >> $GITHUB_OUTPUT
            echo "labels=release:production, folder:${{ inputs.folder }}, sprint:${{ inputs.sprint }}, v${{ inputs.folder }}.${{ inputs.sprint }}.0" >> $GITHUB_OUTPUT
          elif [[ "${{ inputs.target }}" == "RIL Production" ]]; then
            echo "title=chore(release): v${{ inputs.folder }}.${{ inputs.sprint }}.0 [Sprint ${{ inputs.sprint }} RIL Deployment]" >> $GITHUB_OUTPUT
            echo "head=release/production" >> $GITHUB_OUTPUT
            echo "base=release/ril-production" >> $GITHUB_OUTPUT
            echo "type=ril-production" >> $GITHUB_OUTPUT
            echo "labels=release:ril-production, folder:${{ inputs.folder }}, sprint:${{ inputs.sprint }}, v${{ inputs.folder }}.${{ inputs.sprint }}.0" >> $GITHUB_OUTPUT
          fi

  create-pr:
    needs: prepare-inputs
    uses: sensehawk/workflows/.github/workflows/create-pr.yml@main
    secrets: inherit
    permissions:
      issues: write
      contents: read
      pull-requests: write
    with:
      type: "release"
      title: ${{ needs.prepare-inputs.outputs.title }}
      head: ${{ needs.prepare-inputs.outputs.head }}
      base: ${{ needs.prepare-inputs.outputs.base }}
      labels: ${{ needs.prepare-inputs.outputs.labels }}
      auto_merge: true
