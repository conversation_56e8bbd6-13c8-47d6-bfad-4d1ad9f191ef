name: "🤖 | Deploy"

on:
  push:
    branches:
      - "release/development"
      - "release/qa"
      - "release/production"
      - "release/ril-production"
      - "main"

env:
  # NXT-specific module identifiers
  NXT_MODULE_IDENTIFIERS: |
    {
      "sample-flask": "sample_flask"
    }

jobs:
  deploy:
    # if this workflow is running from a SenseHawk repo
    if: ${{ startsWith(github.repository, 'sensehawk/') }}
    uses: sensehawk/workflows/.github/workflows/deploy.yml@main
    secrets: inherit

  deploy-external:
    # if this workflow is running from a NXT repo (should only run for the main branch)
    if: ${{ !startsWith(github.repository, 'sensehawk/') && github.ref == 'refs/heads/main' }}
    name: Deploy
    runs-on: ubuntu-latest
    outputs:
      short_sha: ${{ steps.get_sha.outputs.sha7 }}
      start_time: ${{ steps.set_time.outputs.start_time }}
    steps:
      # Preparation steps
      - name: Set start time
        id: set_time
        run: |
          START_TIME=$(date +%s)
          echo "start_time=${START_TIME}" >> $GITHUB_OUTPUT

      - name: Get short SHA
        id: get_sha
        run: |
          echo "Full SHA: ${GITHUB_SHA}"
          SHORT_SHA=$(echo ${GITHUB_SHA} | cut -c1-7)
          echo "Short SHA: ${SHORT_SHA}"
          echo "sha7=${SHORT_SHA}" >> $GITHUB_OUTPUT

      - name: Get module name
        id: module-id
        run: |
          REPO_NAME=$(echo "${{ github.repository }}" | cut -d '/' -f 2)
          MODULE_MAP='${{ env.NXT_MODULE_IDENTIFIERS }}'
          MODULE_ID=$(echo "$MODULE_MAP" | jq -r --arg repo "$REPO_NAME" '.[$repo]')
          if [[ "$MODULE_ID" == "null" || -z "$MODULE_ID" ]]; then
            echo "Error: No module identifier found for repository $REPO_NAME"
            exit 1
          fi
          echo "module-id=${MODULE_ID}" >> $GITHUB_OUTPUT

      - name: Checkout repository
        uses: actions/checkout@v4

      # Docker build and push steps
      - name: Determine Docker image name
        id: image-name
        run: |
          MODULE=${{ steps.module-id.outputs.module-id }}
          IMAGE_NAME="nextracker/${MODULE}"
          echo "name=${IMAGE_NAME}" >> $GITHUB_OUTPUT

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.NXT_DOCKER_USERNAME }}
          password: ${{ secrets.NXT_DOCKER_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push Docker image
        id: docker_build
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ${{ steps.image-name.outputs.name }}:${{ steps.get_sha.outputs.sha7 }}
          build-args: |
            DD_GIT_REPOSITORY_URL=${{ github.server_url }}/${{ github.repository }}
            DD_GIT_COMMIT_SHA=${{ github.sha }}
            DISABLE_DDTRACE=false
          labels: |
            com.datadog.git.repository_url=${{ github.server_url }}/${{ github.repository }}
            com.datadog.git.commit_sha=${{ github.sha }}

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}

      # AWS Lambda deployment
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-access-key-id: ${{ secrets.NXT_AWS_LAMBDA_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.NXT_AWS_LAMBDA_SECRET_ACCESS_KEY }}
          aws-region: "us-east-1"

      - name: Invoke Lambda function (NXT Production)
        id: invoke_lambda_function
        run: |
          RESPONSE=$(aws lambda invoke \
            --function-name "nxt-deploy_nomad_job-lambda" \
            --cli-binary-format raw-in-base64-out \
            --payload '{"job": "${{ steps.module-id.outputs.module-id }}", "hash": "${{ steps.get_sha.outputs.sha7 }}"}' \
            response.json)

          # Get the response body
          RESPONSE_BODY=$(cat response.json)

          # Parse and format the evaluation details
          EVAL_ID=$(echo $RESPONSE_BODY | jq -r '.body.EvalID')
          STATUS_CODE=$(echo $RESPONSE_BODY | jq -r '.statusCode')
          WARNINGS=$(echo $RESPONSE_BODY | jq -r '.body.Warnings')
          WARNINGS_MSG=$([ -z "$WARNINGS" ] && echo "None" || echo "$WARNINGS")

          # Create a summary of the lambda invocation
          echo "## Lambda Invocation Summary" >> $GITHUB_STEP_SUMMARY
          echo "### Function Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Function:** nxt-deploy_nomad_job-lambda" >> $GITHUB_STEP_SUMMARY
          echo "- **Job Name:** ${{ steps.module-id.outputs.module-id }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit:** ${{ steps.get_sha.outputs.sha7 }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Region:** us-east-1" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Response Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Status Code:** ${STATUS_CODE}" >> $GITHUB_STEP_SUMMARY
          echo "- **Evaluation ID:** ${EVAL_ID}" >> $GITHUB_STEP_SUMMARY
          echo "- **Warnings:** ${WARNINGS_MSG}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "<details><summary><strong>Raw Response</strong></summary>" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo '```json' >> $GITHUB_STEP_SUMMARY
          echo "$RESPONSE_BODY" | jq '.' >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "</details>" >> $GITHUB_STEP_SUMMARY

          # Set the output for other steps to use if needed
          echo "response=$RESPONSE_BODY" >> $GITHUB_OUTPUT
          echo "eval_id=$EVAL_ID" >> $GITHUB_OUTPUT
          echo "status_code=$STATUS_CODE" >> $GITHUB_OUTPUT

      - name: Log output to console
        run: |
          echo "Status Code: ${{ steps.invoke_lambda_function.outputs.status_code }}"
          echo "Evaluation ID: ${{ steps.invoke_lambda_function.outputs.eval_id }}"

  slack:
    name: "Send notification to Slack"
    needs: [deploy-external]
    runs-on: ubuntu-latest
    if: ${{ (success() || failure()) && !startsWith(github.repository, 'sensehawk/') && github.ref == 'refs/heads/main' }}
    steps:
      - name: Get repository name
        id: repo-name
        run: |
          REPO_NAME=$(echo "${{ github.repository }}" | cut -d '/' -f 2)
          echo "repo_name=${REPO_NAME}" >> $GITHUB_OUTPUT

      - name: Calculate duration
        id: duration
        run: |
          START_TIME=${{ needs.deploy-external.outputs.start_time }}
          END_TIME=$(date +%s)
          DURATION=$((END_TIME - START_TIME))

          # Format duration as minutes:seconds
          MINS=$((DURATION / 60))
          SECS=$((DURATION % 60))
          FORMATTED_DURATION="${MINS}m ${SECS}s"

          echo "duration=${FORMATTED_DURATION}" >> $GITHUB_OUTPUT

      - name: Determine deployment status
        id: deployment-status
        run: |
          DEPLOY_STATUS="${{ needs.deploy-external.result }}"

          if [[ "$DEPLOY_STATUS" == "success" ]]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "color=#36a64f" >> $GITHUB_OUTPUT  # Green
            echo "status_text=completed" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "color=#dc3545" >> $GITHUB_OUTPUT  # Red
            echo "status_text=failed" >> $GITHUB_OUTPUT
          fi

      - name: Create Slack message JSON
        id: slack-message
        run: |
          # Escape commit message for JSON
          COMMIT_MSG=$(echo '${{ github.event.head_commit.message }}' | head -n 1 | sed 's/"/\\"/g' | sed "s/'/\\'/g")
          if [[ -z "$COMMIT_MSG" ]]; then
            COMMIT_MSG="No commit message"
          fi

          # Create the JSON payload for Slack
          cat > slack_message.json << 'EOF'
          {
            "attachments": [
              {
                "color": "${{ steps.deployment-status.outputs.color }}",
                "title": "NXT Deployment ${{ steps.deployment-status.outputs.status_text }}: ${{ github.repository }}",
                "title_link": "${{ github.server_url }}/${{ github.repository }}",
                "fields": [
                  {
                    "title": "Branch",
                    "value": "${{ github.ref_name }}",
                    "short": true
                  },
                  {
                    "title": "Commit ID",
                    "value": "<${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|${{ needs.deploy-external.outputs.short_sha }}>",
                    "short": true
                  }
                ],
                "actions": [
                  {
                    "type": "button",
                    "text": "View Deployment Log",
                    "url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                ],
                "footer": "${{ steps.duration.outputs.duration }}",
                "ts": "${{ needs.deploy-external.outputs.start_time }}"
              }
            ]
          }
          EOF

          echo "Generated Slack message:"
          cat slack_message.json

      - name: Post to Slack channel via webhook
        run: |
          # Use NXT-specific webhook URL
          WEBHOOK_URL="${{ secrets.NXT_SLACK_WEBHOOK_URL }}"
          echo "Using NXT webhook for repository: ${{ github.repository }}"

          HTTP_STATUS=$(curl -w "%{http_code}" -s -o /tmp/slack_response.txt \
            -X POST -H 'Content-type: application/json' \
            --data @slack_message.json \
            $WEBHOOK_URL)

          if [[ $HTTP_STATUS -ge 200 && $HTTP_STATUS -lt 300 ]]; then
            echo "✅ Slack notification sent successfully (HTTP $HTTP_STATUS)"
          else
            echo "❌ Failed to send Slack notification (HTTP $HTTP_STATUS)"
            echo "Response:"
            cat /tmp/slack_response.txt
            exit 1
          fi
