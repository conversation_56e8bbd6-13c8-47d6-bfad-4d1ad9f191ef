name: "♻️ | Deploy Using <PERSON><PERSON> and <PERSON><PERSON>"

on:
  workflow_call:
    inputs:
      send-slack-notification:
        required: false
        type: boolean
        default: true
        description: "Whether to send a Slack notification when deployment completes"

env:
  TEST_REPOS: '["sensehawk/sample-flask", "sensehawk/nxt-sh-sample-flask"]'
  MODULE_IDENTIFIERS: |
    {
      "core-backend": "core",
      "nxt-sh-core": "core",
      "terra-backend": "terra",
      "nxt-sh-terra-backend": "terra",
      "jobs-backend": "jobs",
      "nxt-sh-jobs-backend": "jobs",
      "sample-flask": "sample_flask",
      "nxt-sh-sample-flask": "sample_flask",
      "storage-backend": "storage",
      "nxt-sh-storage-backend": "storage",
      "auth-middleware-backend": "auth_middleware",
      "nxt-sh-auth-middleware-backend": "auth_middleware",
      "forms-backend": "forms",
      "inventory-backend": "inventory",
      "system-model-backend": "system_model",
      "therm-backend": "therm",
      "dms-backend": "dms",
      "processing-backend": "processing",
      "cnc-backend": "cnc",
      "approval-flow-backend": "approvals",
      "annotations-backend": "annotations",
      "project-management-backend": "pm",
      "tasks-backend": "tasks",
      "work-backend": "work",
      "naavix-backend": "naavix",
      "lists-spreadsheets-backend": "lns"
    }

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name == 'release/development' && 'Development' || github.ref_name == 'release/qa' && 'QA' || github.ref_name == 'release/production' && 'Production' || github.ref_name == 'release/ril-production' && 'RIL Production' || github.ref_name }}
    outputs:
      short_sha: ${{ steps.get_sha.outputs.sha7 }}
      start_time: ${{ steps.set_time.outputs.start_time }}
    steps:
      # Preparation steps
      - name: Set start time
        id: set_time
        run: |
          START_TIME=$(date +%s)
          echo "start_time=${START_TIME}" >> $GITHUB_OUTPUT

      - name: Get short SHA
        id: get_sha
        run: |
          echo "Full SHA: ${GITHUB_SHA}"
          SHORT_SHA=$(echo ${GITHUB_SHA} | cut -c1-7)
          echo "Short SHA: ${SHORT_SHA}"
          echo "sha7=${SHORT_SHA}" >> $GITHUB_OUTPUT

      - name: Get module name
        id: module-id
        run: |
          REPO_NAME=$(echo "${{ github.repository }}" | cut -d '/' -f 2)
          MODULE_MAP='${{ env.MODULE_IDENTIFIERS }}'
          MODULE_ID=$(echo "$MODULE_MAP" | jq -r --arg repo "$REPO_NAME" '.[$repo]')
          if [[ "$MODULE_ID" == "null" || -z "$MODULE_ID" ]]; then
            echo "Error: No module identifier found for repository $REPO_NAME"
            exit 1
          fi
          echo "module-id=${MODULE_ID}" >> $GITHUB_OUTPUT

      # Docker build and push steps
      - name: Determine Docker image name
        id: image-name
        run: |
          BRANCH=${{ github.ref_name }}
          MODULE=${{ steps.module-id.outputs.module-id }}

          case "$BRANCH" in
            "release/development")
              SUFFIX="development"
              ;;
            "release/qa")
              SUFFIX="qa"
              ;;
            "release/production"|"release/ril-production")
              SUFFIX="production"
              ;;
            *)
              echo "Error: Unsupported branch $BRANCH"
              exit 1
              ;;
          esac

          IMAGE_NAME="sensehawk/${MODULE}_${SUFFIX}"
          echo "name=${IMAGE_NAME}" >> $GITHUB_OUTPUT

      - name: Checkout workflows repository
        uses: actions/checkout@v4
        with:
          repository: sensehawk/workflows
          path: .github/workflows
          token: ${{ secrets.MAINTENANCE_PAT }}

      - name: Build and push Docker image
        if: startsWith(github.repository, 'sensehawk') && !startsWith(github.repository, 'sensehawk/nxt-sh-')
        uses: ./.github/workflows/.github/actions/docker-build-push
        with:
          docker-image-name: ${{ steps.image-name.outputs.name }}
          short-commit-hash: ${{ steps.get_sha.outputs.sha7 }}
          dockerhub-username: ${{ secrets.DOCKERHUB_USERNAME }}
          dockerhub-token: ${{ secrets.DOCKERHUB_TOKEN }}
          github-server-url: ${{ github.server_url }}
          github-repository: ${{ github.repository }}
          github-sha: ${{ github.sha }}

      - name: Build and push Docker image (NXT)
        if: github.ref_name == 'release/production' && startsWith(github.repository, 'sensehawk/nxt-sh-')
        uses: ./.github/workflows/.github/actions/docker-build-push
        with:
          docker-image-name: "sensehawk/nxt_sh_${{ steps.module-id.outputs.module-id }}"
          short-commit-hash: ${{ steps.get_sha.outputs.sha7 }}
          dockerhub-username: ${{ secrets.NXT_DOCKER_USERNAME }}
          dockerhub-token: ${{ secrets.NXT_DOCKER_TOKEN }}
          github-server-url: ${{ github.server_url }}
          github-repository: ${{ github.repository }}
          github-sha: ${{ github.sha }}

      # Lambda deployment steps
      - name: Invoke Lambda function (Development)
        if: github.ref_name == 'release/development'
        uses: ./.github/workflows/.github/actions/lambda-invoke
        with:
          aws-access-key: ${{ secrets.TM_DEV_DEPLOY_NOMAD_JOB_ACCESS_KEY_ID }}
          aws-secret-key: ${{ secrets.TM_DEV_DEPLOY_NOMAD_JOB_SECRET_ACCESS_KEY }}
          aws-region: "us-east-1"
          function-name: "tm-dev-deploy-nomad-job"
          job-name: ${{ steps.module-id.outputs.module-id }}
          short-commit-hash: ${{ steps.get_sha.outputs.sha7 }}

      - name: Invoke Lambda function (QA)
        if: github.ref_name == 'release/qa'
        uses: ./.github/workflows/.github/actions/lambda-invoke
        with:
          aws-access-key: ${{ secrets.TM_QA_DEPLOY_NOMAD_JOB_ACCESS_KEY_ID }}
          aws-secret-key: ${{ secrets.TM_QA_DEPLOY_NOMAD_JOB_SECRET_ACCESS_KEY }}
          aws-region: "us-east-1"
          function-name: "tm-qa-deploy-nomad-job"
          job-name: ${{ steps.module-id.outputs.module-id }}
          short-commit-hash: ${{ steps.get_sha.outputs.sha7 }}

      - name: Invoke Lambda function (Production)
        if: github.ref_name == 'release/production' && !startsWith(github.repository, 'sensehawk/nxt-sh-')
        uses: ./.github/workflows/.github/actions/lambda-invoke
        with:
          aws-access-key: ${{ secrets.TM_PROD_DEPLOY_NOMAD_JOB_ACCESS_KEY_ID }}
          aws-secret-key: ${{ secrets.TM_PROD_DEPLOY_NOMAD_JOB_SECRET_ACCESS_KEY }}
          aws-region: "us-east-1"
          function-name: "tm-prod-deploy-nomad-job"
          job-name: ${{ steps.module-id.outputs.module-id }}
          short-commit-hash: ${{ steps.get_sha.outputs.sha7 }}

      - name: Invoke Lambda function (RIL Production)
        if: github.ref_name == 'release/ril-production'
        uses: ./.github/workflows/.github/actions/lambda-invoke
        with:
          aws-access-key: ${{ secrets.RIL_ACCESS_KEY }}
          aws-secret-key: ${{ secrets.RIL_SECRET_ACCESS_KEY }}
          aws-region: "us-east-1"
          function-name: "deployNomadJob"
          job-name: ${{ steps.module-id.outputs.module-id }}
          short-commit-hash: ${{ steps.get_sha.outputs.sha7 }}

      - name: Invoke Lambda function (NXT Production)
        if: github.ref_name == 'release/production' && startsWith(github.repository, 'sensehawk/nxt-sh-')
        uses: ./.github/workflows/.github/actions/lambda-invoke
        with:
          aws-access-key: ${{ secrets.NXT_SH_AWS_LAMBDA_ACCESS_KEY }}
          aws-secret-key: ${{ secrets.NXT_SH_AWS_LAMBDA_SECRET_ACCESS_KEY }}
          aws-region: "us-east-1"
          function-name: "nxt-sh-deploy_nomad_job-lambda"
          job-name: "nxt_sh_${{ steps.module-id.outputs.module-id }}"
          short-commit-hash: ${{ steps.get_sha.outputs.sha7 }}

  slack:
    name: "Send notification to Slack"
    needs: [deploy]
    runs-on: ubuntu-latest
    if: ${{ (success() || failure()) && inputs.send-slack-notification && (github.ref_name != 'release/development') }}
    steps:
      - name: Get repository name
        id: repo-name
        run: |
          REPO_NAME=$(echo "${{ github.repository }}" | cut -d '/' -f 2)
          echo "repo_name=${REPO_NAME}" >> $GITHUB_OUTPUT

      - name: Calculate duration
        id: duration
        run: |
          START_TIME=${{ needs.deploy.outputs.start_time }}
          END_TIME=$(date +%s)
          DURATION=$((END_TIME - START_TIME))

          # Format duration as minutes:seconds
          MINS=$((DURATION / 60))
          SECS=$((DURATION % 60))
          FORMATTED_DURATION="${MINS}m ${SECS}s"

          echo "duration=${FORMATTED_DURATION}" >> $GITHUB_OUTPUT

      - name: Determine deployment status
        id: deployment-status
        run: |
          DEPLOY_STATUS="${{ needs.deploy.result }}"

          if [[ "$DEPLOY_STATUS" == "success" ]]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "color=#36a64f" >> $GITHUB_OUTPUT  # Green
            echo "status_text=completed" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "color=#dc3545" >> $GITHUB_OUTPUT  # Red
            echo "status_text=failed" >> $GITHUB_OUTPUT
          fi

      - name: Create Slack message JSON
        id: slack-message
        run: |
          # Escape commit message for JSON
          COMMIT_MSG=$(echo '${{ github.event.head_commit.message }}' | head -n 1 | sed 's/"/\\"/g' | sed "s/'/\\'/g")
          if [[ -z "$COMMIT_MSG" ]]; then
            COMMIT_MSG="No commit message"
          fi

          # Create the JSON payload for Slack
          cat > slack_message.json << 'EOF'
          {
            "attachments": [
              {
                "color": "${{ steps.deployment-status.outputs.color }}",
                "title": "Deployment ${{ steps.deployment-status.outputs.status_text }}: ${{ github.repository }}",
                "title_link": "${{ github.server_url }}/${{ github.repository }}",
                "fields": [
                  {
                    "title": "Branch",
                    "value": "${{ github.ref_name }}",
                    "short": true
                  },
                  {
                    "title": "Commit ID",
                    "value": "<${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|${{ needs.deploy.outputs.short_sha }}>",
                    "short": true
                  }
                ],
                "actions": [
                  {
                    "type": "button",
                    "text": "View Deployment Log",
                    "url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                ],
                "footer": "${{ steps.duration.outputs.duration }}",
                "ts": "${{ needs.deploy.outputs.start_time }}"
              }
            ]
          }
          EOF

          echo "Generated Slack message:"
          cat slack_message.json

      - name: Post to Slack channel via webhook
        run: |
          # Determine which webhook URL to use based on repository
          TEST_REPOS='${{ env.TEST_REPOS }}'
          CURRENT_REPO="${{ github.repository }}"

          # Check if current repository is in TEST_REPOS array
          if echo "$TEST_REPOS" | jq -r '.[]' | grep -q "^$CURRENT_REPO$"; then
            WEBHOOK_URL="${{ secrets.SLACK_WEBHOOK_URL_CH_PLAYGROUND }}"
            echo "Using playground webhook for test repository: $CURRENT_REPO"
          else
            WEBHOOK_URL="${{ secrets.SLACK_WEBHOOK_URL_CH_DEPLOYMENTS }}"
            echo "Using deployments webhook for production repository: $CURRENT_REPO"
          fi

          HTTP_STATUS=$(curl -w "%{http_code}" -s -o /tmp/slack_response.txt \
            -X POST -H 'Content-type: application/json' \
            --data @slack_message.json \
            $WEBHOOK_URL)

          if [[ $HTTP_STATUS -ge 200 && $HTTP_STATUS -lt 300 ]]; then
            echo "✅ Slack notification sent successfully (HTTP $HTTP_STATUS)"
          else
            echo "❌ Failed to send Slack notification (HTTP $HTTP_STATUS)"
            echo "Response:"
            cat /tmp/slack_response.txt
            exit 1
          fi
