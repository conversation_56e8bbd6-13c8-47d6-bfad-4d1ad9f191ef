name: "♻️ | Branch Merge"

on:
  workflow_call:
    inputs:
      source_branch:
        description: "Branch to merge from"
        required: false
        type: string
        default: "main"
      target_branch:
        description: "Branch to merge into"
        required: false
        type: string
        default: "release/development"
      merge_strategy:
        description: "Git merge strategy (--ff, --no-ff, --squash)"
        required: false
        type: string
        default: "--no-ff"

jobs:
  merge:
    runs-on: ubuntu-latest
    steps:
      - run: echo "🚀 Triggered by ${{ github.event_name }}"

      - name: Checkout workflows repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.MAINTENANCE_PAT }}

      - name: Configure Git
        run: |
          git config user.name 'sensehawk-bot'
          git config user.email '<EMAIL>'

      - name: Fetch all branches
        run: |
          git fetch origin
          git branch -r

      - name: Verify branches exist
        run: |
          if ! git show-ref --verify --quiet refs/remotes/origin/${{ inputs.source_branch }}; then
            echo "Source branch ${{ inputs.source_branch }} does not exist!"
            exit 1
          fi
          if ! git show-ref --verify --quiet refs/remotes/origin/${{ inputs.target_branch }}; then
            echo "Target branch ${{ inputs.target_branch }} does not exist!"
            exit 1
          fi

      - name: Merge branches
        run: |
          git checkout ${{ inputs.target_branch }}
          git merge ${{ inputs.merge_strategy }} origin/${{ inputs.source_branch }} -m "chore: merge ${{ inputs.source_branch }} into ${{ inputs.target_branch }}"
          git push origin ${{ inputs.target_branch }}

      - run: echo "⏹ Status - ${{ job.status }}"
