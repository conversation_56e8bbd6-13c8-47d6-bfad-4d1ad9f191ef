name: "♻️ | Process Pull Request"

on:
  workflow_call:

env:
  HOTFIX_LABELS: '["hotfix", "automation:mergeback"]'
  QA_LABELS: '["qa", "automation:mergeback"]'
  # Anomaly labels
  NO_CLICKUP_ID_LABELS: '["anomaly:no-clickup-task"]'
  NO_ASSOCIATED_SPRINT_LABELS: '["anomaly:no-associated-sprint"]'
  DEFAULT_SPRINT_LABELS: '["anomaly:default-sprint-assumed"]'
  # TODO: use this
  MULTIPLE_CLICKUP_ID_LABELS: '["anomaly:multiple-clickup-tasks"]'
  # ClickUp Team ID
  CLICKUP_TEAM_ID: "3620712"
  # Coda Document & Table IDs
  CODA_DOC_ID: "yFTJGunpbF"
  CODA_TABLE_ID: "grid-yt4ZnB7tjW"
  # Coda Column IDs
  CODA_COLUMN_SPRINT: "c-vS7-NZOI99"
  CODA_COLUMN_MODULE: "c-Ja97EuxwqL"
  CODA_COLUMN_SUBMISSION_TYPE: "c-h5ZurBq3Op"
  CODA_COLUMN_PR_AUTHOR_EMAIL: "c-qGFYsCBdDy"
  CODA_COLUMN_REASON: "c-Bh4QhYkBrx"
  CODA_COLUMN_ENVIRONMENT: "c-5XM9RQ3QPM"
  CODA_COLUMN_CLICKUP_URL: "c-8MmBKZYk2k"
  CODA_COLUMN_PR_URL: "c-HxPA7V0Me0"
  CODA_BUTTON_DEPLOYED: "c-k8_bvCn25n"
  # Github user to Slack ID map
  GITHUB_SLACK_MAP: '{
    "ajayrsh": "<EMAIL>",
    "Akshitr-sh": "<EMAIL>",
    "Anil-Pujeri": "<EMAIL>",
    "anubhuti-sh": "<EMAIL>",
    "anuj97541": "<EMAIL>",
    "anujkumar05": "<EMAIL>",
    "bharath-sensehawk": "<EMAIL>",
    "casticjehin": "<EMAIL>",
    "deepakSenseHawk": "<EMAIL>",
    "gautham-8": "<EMAIL>",
    "karthikeyanVue": "<EMAIL>",
    "kdyadav": "<EMAIL>",
    "kirnh": "<EMAIL>",
    "mdaffan01": "<EMAIL>",
    "Mondal10": "<EMAIL>",
    "PardeepBaboria": "<EMAIL>",
    "poornimabyregowda19": "<EMAIL>",
    "prajwalbharadwaj": "<EMAIL>",
    "saideeptalari": "<EMAIL>",
    "sh-ravan": "<EMAIL>",
    "somasekharkakarla": "<EMAIL>",
    "tebbythomas": "<EMAIL>",
    "vijaymadhavan": "<EMAIL>",
    "VinitaPotter": "<EMAIL>"
  }'
  # Module names mapping
  MODULE_NAMES: '{
    "core-backend": "Core",
    "forms-backend": "Forms",
    "inventory-backend": "Inventory",
    "system-model-backend": "System model",
    "terra-backend": "Terra",
    "therm-backend": "Therm",
    "dms-backend": "DMS",
    "processing-backend": "Processing",
    "cnc-backend": "Commissioning & Certification",
    "approval-flow-backend": "Approval flows",
    "annotations-backend": "Annotations",
    "project-management-backend": "Project Management",
    "tasks-backend": "Tasks",
    "work-backend": "Plan view",
    "taskmapper-frontend": "Frontend",
    "taskmapper-mobile": "Mobile",
    "auth-middleware-backend": "Auth Middleware",
    "lists-spreadsheets-backend": "Lists & Spreadsheets",
    "storage-backend": "Storage",
    "naavix-backend": "NaaviX"
  }'

permissions:
  issues: write
  contents: write
  pull-requests: write

jobs:
  log-info:
    runs-on: ubuntu-latest
    steps:
      - name: Log Information
        run: |
          echo "## Workflow Information" >> $GITHUB_STEP_SUMMARY
          echo "| Property | Value |" >> $GITHUB_STEP_SUMMARY
          echo "| --- | --- |" >> $GITHUB_STEP_SUMMARY
          echo "| Event | ${{ github.event_name }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Action | ${{ github.event.action }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Actor | ${{ github.actor }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Repository | ${{ github.repository }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Ref | ${{ github.ref }} |" >> $GITHUB_STEP_SUMMARY
          echo "| SHA | ${{ github.sha }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Run ID | ${{ github.run_id }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Run Number | ${{ github.run_number }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Run Attempt | ${{ github.run_attempt }} |" >> $GITHUB_STEP_SUMMARY

  bump-tag-and-release:
    if: github.event.pull_request.merged == true && github.event.pull_request.base.ref == 'release/production'
    uses: sensehawk/workflows/.github/workflows/bump-tag-release.yml@main
    permissions:
      contents: write
    secrets: inherit

  pr-closure:
    name: "Handle PR Closure Pre-check"
    runs-on: ubuntu-latest
    outputs:
      action_taken: ${{ steps.closure_check.outputs.action_taken }}
    permissions:
      issues: write
      contents: write
      pull-requests: write
    steps:
      - name: Check and Close Invalid PRs
        id: closure_check
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.MAINTENANCE_PAT }}
          script: |
            const prAuthor = context.payload.pull_request.user.login;
            const base = context.payload.pull_request.base.ref;
            const head = context.payload.pull_request.head.ref;
            const prNumber = context.issue.number;

            // Function to add comment and close PR
            const closeWithComment = async (message) => {
              console.log('❌ Closing PR with message:', message);
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: prNumber,
                body: message
              });
              await github.rest.pulls.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                pull_number: prNumber,
                state: 'closed'
              });
              console.log('✅ PR closed successfully');
            };

            core.setOutput('action_taken', 'none'); // Default output

            console.log('🔍 Checking PR closure conditions...');
            if (prAuthor !== 'sensehawk-bot') {
              console.log('⚠️ PR author is not sensehawk-bot, but ', prAuthor);
              if ((base === 'release/production' && head === 'release/qa') ||
                  (base === 'release/ril-production' && head === 'release/production')) {
                console.log('❌ Invalid PR flow detected:', { base, head });
                await github.rest.issues.createComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: prNumber,
                  body: `### ⚠️ Unusual flow detected\n\nThis PR from ${head} to ${base} looks unusual. Please ensure you are following the correct Git process.\n\nIgnore if this is intentional.`
                });
                // await closeWithComment(`This PR from ${head} to ${base} was automatically closed as it was not created using the release action. Please ensure you are following the correct release process.`);
                // core.setOutput('action_taken', 'closed_by_flow_check');
                return;
              }
              console.log('✅ PR flow is valid for this check, no action taken by pr-closure job.');
            } else {
              console.log('ℹ️ PR author is sensehawk-bot, skipping specific flow check.');
            }

  process-open-pr:
    name: "Process Open PR (Labeling, ClickUp, Coda)"
    needs: pr-closure
    if: |
      needs.pr-closure.outputs.action_taken != 'closed_by_flow_check' &&
      github.event.pull_request.state == 'open' &&
      contains(fromJSON('["main", "release/development", "release/qa", "release/production", "release/ril-production"]'), github.event.pull_request.base.ref) &&
      !(contains(fromJSON('["labeled", "unlabeled", "edited"]'), github.event.action) && github.actor == 'sensehawk-bot')
    runs-on: ubuntu-latest
    permissions:
      issues: write
      contents: write
      pull-requests: write
    steps:
      - name: Handle Open PR Logic
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.MAINTENANCE_PAT }}
          script: |
            const base = context.payload.pull_request.base.ref;
            const head = context.payload.pull_request.head.ref;
            const prAuthor = context.payload.pull_request.user.login;
            const prTitle = context.payload.pull_request.title;
            const prNumber = context.issue.number;
            const prUrl = context.payload.pull_request.html_url;

            // if the event is edited, wait 5 seconds before doing anything else
            console.log('context.payload?.action: ', context.payload?.action);
            if (context.payload?.action === 'edited') {
              console.log('⏳ Waiting for 5 seconds before processing edited PR...');
              await new Promise(resolve => setTimeout(resolve, 5000));
            }

            console.log('🔍 Processing Open PR:', {
              base, head, prAuthor, prTitle, prNumber, prUrl
            });

            // Function to get PR labels
            const getPRLabels = async () => {
              const { data: labels } = await github.rest.issues.listLabelsOnIssue({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: prNumber
              });
              return labels;
            };

            // Function to set PR labels
            const setPRLabels = async (labelsToAdd, labelsToRemove = '[]') => {
              const labelsToAddArray = JSON.parse(labelsToAdd);
              const labelsToRemoveArray = JSON.parse(labelsToRemove);

              const currentLabels = await getPRLabels();

              console.log('🏷️ Processing labels:', {
                adding: labelsToAddArray,
                removing: labelsToRemoveArray
              });
              const newSprintLabel = labelsToAddArray.find(label => label.startsWith('sprint:'));
              const labelsToRemoveSet = new Set(labelsToRemoveArray);

              if (labelsToAddArray.includes(JSON.parse(process.env.NO_CLICKUP_ID_LABELS)[0]) || newSprintLabel) {
                currentLabels
                  .map(label => label.name)
                  .filter(name => name.startsWith('sprint:'))
                  .forEach(name => labelsToRemoveSet.add(name));
              }

              const newLabels = [
                ...currentLabels
                  .map(label => label.name)
                  .filter(name => !labelsToRemoveSet.has(name)),
                ...labelsToAddArray
              ];
              const uniqueLabels = [...new Set(newLabels)];

              console.log('🏷️ Updating labels:', uniqueLabels);
              await github.rest.issues.setLabels({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: prNumber,
                labels: uniqueLabels
              });
              console.log('✅ Labels updated successfully');
            };

            // Function to manage ClickUp task ID comments
            const handleClickUpTaskIdComment = async (hasValidId) => {
              console.log('🔍 Managing ClickUp task ID comments:', { hasValidId });
              const { data: comments } = await github.rest.issues.listComments({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: prNumber
              });

              // Log out the comments cleanly for debugging
              console.log('💬 Found comments on PR:', comments.length);
              comments.forEach(comment => {
                console.log(`\n- - -\nComment Author: ${comment.user.login}:\nComment Body: ${comment.body}\n`);
              });

              const taskIdComment = comments.find(comment =>
                comment.body.includes('Please update the PR title to include a valid ClickUp task ID')
              );
              if (hasValidId && taskIdComment) {
                console.log('🗑️ Removing outdated task ID comment');
                await github.rest.issues.deleteComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  comment_id: taskIdComment.id
                });
              } else if (!hasValidId && !taskIdComment) {
                console.log('💬 Adding task ID comment');
                await github.rest.issues.createComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: prNumber,
                  body: '### ⚠️ No valid ClickUp task ID found\n\nPlease update the PR title to include a valid ClickUp task ID (format: `TICKET-12345`) to ensure proper tracking and automatic Coda submission.'
                });
              }
            };

            // Function to check for valid ClickUp task ID
            const hasValidClickUpTaskId = (title) => {
              const isValid = /TICKET-\d+/.test(title);
              console.log('🔍 Checking ClickUp Task ID in:', title);
              console.log('📋 Task ID valid?', isValid);
              return isValid;
            };

            // Function to find current sprint based on date
            const findCurrentSprint = () => {
              const sprintDateMap = {
                "Sprint 3": {"from": "8/21/24", "to": "9/3/24"},
                "Sprint 4": {"from": "4/9/24", "to": "17/9/24"},
                "Sprint 5": {"from": "18/9/24", "to": "1/10/24"},
                "Sprint 6": {"from": "2/10/24", "to": "15/10/24"},
                "Sprint 7": {"from": "16/10/24", "to": "29/10/24"},
                "Sprint 8": {"from": "30/10/24", "to": "12/11/24"},
                "Sprint 9": {"from": "13/11/24", "to": "26/11/24"},
                "Sprint 10": {"from": "27/11/24", "to": "10/12/24"},
                "Sprint 11": {"from": "11/12/24", "to": "24/12/24"},
                "Sprint 12": {"from": "25/12/24", "to": "7/1/25"},
                "Sprint 13": {"from": "8/1/25", "to": "21/1/25"},
                "Sprint 14": {"from": "22/1/25", "to": "4/2/25"},
                "Sprint 15": {"from": "5/2/25", "to": "18/2/25"},
                "Sprint 16": {"from": "19/2/25", "to": "4/3/25"},
                "Sprint 17": {"from": "5/3/25", "to": "18/3/25"},
                "Sprint 18": {"from": "19/3/25", "to": "1/4/25"},
                "Sprint 19": {"from": "2/4/25", "to": "15/4/25"},
                "Sprint 20": {"from": "16/4/25", "to": "29/4/25"},
                "Sprint 21": {"from": "30/4/25", "to": "13/5/25"},
                "Sprint 22": {"from": "14/5/25", "to": "27/5/25"},
                "Sprint 23": {"from": "28/5/25", "to": "10/6/25"},
                "Sprint 24": {"from": "11/6/25", "to": "24/6/25"},
                "Sprint 25": {"from": "25/6/25", "to": "8/7/25"},
              }
              const date = new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }));
              const currentDate = `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear().toString().slice(-2)}`;
              const [currentDay, currentMonth, currentYear] = currentDate.split('/');
              const currentDateObj = new Date(`20${currentYear}`, parseInt(currentMonth) - 1, parseInt(currentDay));
              for (const [currentSprintName, dates] of Object.entries(sprintDateMap)) {
                const [fromDay, fromMonth, fromYear] = dates.from.split('/');
                const [toDay, toMonth, toYear] = dates.to.split('/');
                const fromDate = new Date(`20${fromYear || '24'}`, parseInt(fromMonth) - 1, parseInt(fromDay));
                const toDate = new Date(`20${toYear || '24'}`, parseInt(toMonth) - 1, parseInt(toDay));
                if (currentDateObj >= fromDate && currentDateObj <= toDate) {
                  console.log('📅 Current sprint:', { currentDate, currentSprintName });
                  const currentSprintNumber = currentSprintName.split(' ')[1];
                  return { currentSprintName, currentSprintNumber };
                }
              }
              return { currentSprintName: null, currentSprintNumber: null };
            };

            // Function to fetch ClickUp task details
            const fetchClickUpTaskDetails = async (taskId) => {
              let sprintName = null;
              let sprintNumber = null;
              let taskName = '';
              let correctedTaskId = taskId;

              const options = {
                method: 'GET',
                headers: { accept: 'application/json', Authorization: '${{ secrets.CLICKUP_TOKEN }}' }
              };
              try {
                console.log(`🔄 Fetching ClickUp task details for https://app.clickup.com/t/${process.env.CLICKUP_TEAM_ID}/${taskId}`);
                const response = await fetch(`https://api.clickup.com/api/v2/task/${taskId}/?custom_task_ids=true&team_id=${process.env.CLICKUP_TEAM_ID}`, options);
                let taskDetails = await response.json();

                if (taskDetails.top_level_parent) {
                  console.log('🔄 Fetching top-level parent task details...');
                  const parentResponse = await fetch(`https://api.clickup.com/api/v2/task/${taskDetails.top_level_parent}/?custom_task_ids=false&team_id=${process.env.CLICKUP_TEAM_ID}`, options);
                  const parentDetails = await parentResponse.json();
                  taskDetails = parentDetails;

                  // Leave a comment and replace the task ID in the PR title with the top-level parent task ID
                  correctedTaskId = taskDetails.custom_id;
                  const updatedTitle = prTitle.replace(/TICKET-\d+/, correctedTaskId);
                  console.log('🔄 Updating PR title with top-level parent task ID:', updatedTitle);
                  await github.rest.issues.createComment({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    issue_number: prNumber,
                    body: `The task ID has been updated to the top-level parent task: ${correctedTaskId}`
                  });
                  await github.rest.pulls.update({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    pull_number: prNumber,
                    title: updatedTitle
                  });
                }

                if (taskDetails.locations && taskDetails.locations.length > 0) {
                  const sprintLocations = taskDetails.locations
                    .filter(loc => /Sprint \d+/.test(loc.name))
                    .map(loc => ({
                      ...loc,
                      sprintNumber: parseInt(loc.name.match(/Sprint (\d+)/)?.[1] || '0', 10),
                      shortName: loc.name.match(/Sprint \d+/)?.[0] || ''
                    }))
                    .sort((a, b) => b.sprintNumber - a.sprintNumber);

                  if (sprintLocations.length > 0 && sprintLocations[0]?.shortName?.match?.(/Sprint \d+/)) {
                    sprintName = sprintLocations[0].shortName;
                    sprintNumber = sprintName.split(' ')[1];
                  }
                }

                if (!sprintNumber) {
                  console.log('⚠️ No sprint location found in task details. Attempting to find current sprint...');
                  let { currentSprintName = null, currentSprintNumber = null } = findCurrentSprint();
                  console.log('🔄 Current sprint details:', { currentSprintName, currentSprintNumber });

                  await github.rest.issues.createComment({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    issue_number: prNumber,
                    body: `[${correctedTaskId}](https://app.clickup.com/t/3620712/${correctedTaskId}) does not belong to a sprint list. ${currentSprintName ? `Current sprint (${currentSprintName}) is assumed.` : `Please add the task to a sprint.`}`
                  });
                  if (currentSprintName) {
                    sprintName = currentSprintName;
                    sprintNumber = currentSprintNumber;
                    await github.rest.issues.addLabels({
                      owner: context.repo.owner,
                      repo: context.repo.repo,
                      issue_number: prNumber,
                      labels: JSON.parse(process.env.DEFAULT_SPRINT_LABELS)
                    });
                  }
                }

                taskName = taskDetails.name || '';
                console.log('✅ ClickUp task details fetched:', { taskName, sprintName, sprintNumber });
              } catch (error) {
                console.error('❌ Failed to fetch task details from ClickUp:', error);
              }
              return { taskName, sprintName, sprintNumber };
            };

            // Function to submit to Coda
            const submitToCoda = async (taskId, sprintName, environment, reason = '') => {
              console.log('📝 Preparing Coda submission data... Params received: ', { taskId, sprintName, environment, reason });
              const clickupUrl = taskId ? `https://app.clickup.com/t/${process.env.CLICKUP_TEAM_ID}/${taskId}` : 'N/A';
              const moduleNames = JSON.parse(process.env.MODULE_NAMES);
              const repoName = context.repo.repo;
              const moduleName = moduleNames[repoName] || repoName;
              console.log('📦 Resolved module name:', { repoName, moduleName });
              let githubUser = prAuthor;
              if (githubUser === 'sensehawk-bot') {
                console.log('⚠️ PR author is sensehawk-bot, using contributors action...');
                try {
                  const { data: contributors } = await github.request('GET /repos/{owner}/{repo}/contributors', {
                    owner: context.repo.owner, repo: context.repo.repo, per_page: 1,
                    headers: { 'X-GitHub-Api-Version': '2022-11-28' }
                  });
                  if (contributors && contributors.length > 0) githubUser = contributors[0].login;
                  else githubUser = 'sh-ravan';
                  console.log('✅ Using determined contributor:', githubUser);
                } catch (error) {
                  console.error('❌ Error getting contributors:', error); githubUser = 'sh-ravan';
                }
              }
              const rowData = {
                cells: [
                  { column: process.env.CODA_COLUMN_SPRINT, value: sprintName },
                  { column: process.env.CODA_COLUMN_MODULE, value: moduleName },
                  { column: process.env.CODA_COLUMN_PR_AUTHOR_EMAIL, value: JSON.parse(process.env.GITHUB_SLACK_MAP)[githubUser] || '' },
                  { column: process.env.CODA_COLUMN_REASON, value: reason },
                  { column: process.env.CODA_COLUMN_ENVIRONMENT, value: environment },
                  { column: process.env.CODA_COLUMN_CLICKUP_URL, value: clickupUrl },
                  { column: process.env.CODA_COLUMN_PR_URL, value: prUrl },
                  { column: process.env.CODA_COLUMN_SUBMISSION_TYPE, value: 'Automatic' }
                ]
              };
              console.log('📋 Row data prepared for Coda:', rowData);
              try {
                const upsertUrl = `https://coda.io/apis/v1/docs/${process.env.CODA_DOC_ID}/tables/${process.env.CODA_TABLE_ID}/rows`;
                console.log('🔄 Upserting row in Coda...');
                const response = await fetch(upsertUrl, {
                  method: 'POST', headers: { 'Authorization': 'Bearer ${{ secrets.CODA_TOKEN }}', 'Content-Type': 'application/json' },
                  body: JSON.stringify({ rows: [rowData], keyColumns: [process.env.CODA_COLUMN_PR_URL] })
                });
                if (!response.ok) {
                  console.error('❌ Coda API error details:', { status: response.status, statusText: response.statusText, responseText: await response.text() });
                  throw new Error(`HTTP error! status: ${response.status}`);
                }
                console.log('✅ Successfully submitted to Coda');
              } catch (error) {
                console.error('❌ Failed to submit to Coda:', error);
              }
            };

            // Main logic for open PRs
            console.log('✅ PR is open, proceeding with processing...');

            // Handle PRs to main
            if (base === 'main' && !head.startsWith('release/')) {
              console.log('🧪 Processing `main` PR:', { base, head });
              const hasValidId = hasValidClickUpTaskId(prTitle);
              await handleClickUpTaskIdComment(hasValidId);
              if (hasValidId) {
                const taskId = prTitle.match(/TICKET-\d+/)[0];
                console.log('✅ Valid ClickUp task ID found:', taskId);
                const { taskName, sprintName, sprintNumber } = await fetchClickUpTaskDetails(taskId);
                if (sprintNumber && sprintNumber !== 'null')
                  await setPRLabels(`["sprint:${sprintNumber}"]`, process.env.NO_CLICKUP_ID_LABELS);
                else
                  await setPRLabels(process.env.NO_ASSOCIATED_SPRINT_LABELS, process.env.NO_CLICKUP_ID_LABELS);
              } else {
                console.log('⚠️ Invalid or missing ClickUp task ID in PR title');
                await setPRLabels(process.env.NO_CLICKUP_ID_LABELS, '[]');
              }
            }
            // Handle QA PRs
            else if (base === 'release/qa' && head.startsWith('qa/')) {
              console.log('🧪 Processing QA PR:', { base, head });
              await setPRLabels(process.env.QA_LABELS, process.env.HOTFIX_LABELS);
              const hasValidId = hasValidClickUpTaskId(prTitle);
              await handleClickUpTaskIdComment(hasValidId);
              if (hasValidId) {
                const taskId = prTitle.match(/TICKET-\d+/)[0];
                console.log('✅ Valid ClickUp task ID found:', taskId);
                const { taskName, sprintName, sprintNumber } = await fetchClickUpTaskDetails(taskId);
                if (sprintNumber && sprintNumber !== 'null')
                  await setPRLabels(`["sprint:${sprintNumber}"]`, process.env.NO_CLICKUP_ID_LABELS);
                else
                  await setPRLabels(process.env.NO_ASSOCIATED_SPRINT_LABELS, process.env.NO_CLICKUP_ID_LABELS);
                await submitToCoda(taskId, sprintName, 'QA', taskName);
              } else {
                console.log('⚠️ Invalid or missing ClickUp task ID in PR title');
                await setPRLabels(process.env.NO_CLICKUP_ID_LABELS, '[]');
              }
            }
            // Handle hotfix PRs
            else if (base === 'release/production' && head.startsWith('hotfix/')) {
              console.log('🔧 Processing hotfix PR:', { base, head });
              await setPRLabels(process.env.HOTFIX_LABELS, process.env.QA_LABELS);
              const hasValidId = hasValidClickUpTaskId(prTitle);
              await handleClickUpTaskIdComment(hasValidId);
              if (hasValidId) {
                const taskId = prTitle.match(/TICKET-\d+/)[0];
                console.log('✅ Valid ClickUp task ID found:', taskId);
                const { taskName, sprintName, sprintNumber } = await fetchClickUpTaskDetails(taskId);
                await setPRLabels(`["sprint:${sprintNumber}"]`, process.env.NO_CLICKUP_ID_LABELS);
                await submitToCoda(taskId, '🔥Hotfix', 'Production', taskName);
              } else {
                console.log('⚠️ Invalid or missing ClickUp task ID in PR title');
                await setPRLabels(process.env.NO_CLICKUP_ID_LABELS, '[]');
              }
            }
            // Handle release PRs
            else {
              const labels = await getPRLabels();
              const hasReleaseLabel = labels.some(label => label.name === 'automation:release');
              const sprintLabel = labels.find(label => label.name.startsWith('sprint:'));
              if (hasReleaseLabel && sprintLabel) {
                console.log('🗓️ Found sprint label in PR:', sprintLabel.name);
                const sprintName = `Sprint ${sprintLabel.name.split(':')[1]}`;
                if (base === 'release/production' && head === 'release/qa') {
                  console.log('📝 Processing regular deployment from QA to Production');
                  await submitToCoda('', sprintName, 'Production', 'Regular Deployment');
                } else if (base === 'release/ril-production' && head === 'release/production') {
                  console.log('📝 Processing regular deployment from Production to RIL Production');
                  await submitToCoda('', sprintName, 'RIL Production', 'Regular Deployment (RIL)');
                }
              }
            }
            console.log('✅ Open PR processing completed');

  process-closed-pr:
    name: "Process Closed PR (Coda Updates, Mergeback)"
    needs: pr-closure
    if: |
      needs.pr-closure.outputs.action_taken != 'closed_by_flow_check' &&
      github.event.pull_request.state == 'closed' &&
      contains(fromJSON('["main", "release/development", "release/qa", "release/production", "release/ril-production"]'), github.event.pull_request.base.ref)
    runs-on: ubuntu-latest
    permissions:
      issues: write
      contents: write
      pull-requests: write
    steps:
      - name: Handle Closed PR Logic
        uses: actions/github-script@v7
        env:
          GH_TOKEN: ${{ secrets.MAINTENANCE_PAT }}
        with:
          github-token: ${{ secrets.MAINTENANCE_PAT }}
          script: |
            const base = context.payload.pull_request.base.ref;
            const prNumber = context.issue.number;
            const prMerged = context.payload.pull_request.merged;
            const prUrl = context.payload.pull_request.html_url;

            console.log('🔍 Processing Closed PR:', {
              base, prNumber, prMerged, prUrl
            });

            // Function to delete Coda entry
            const deleteCodaEntry = async (rowId) => {
              console.log('🗑️ Deleting Coda entry:', { rowId });
              try {
                const deleteUrl = `https://coda.io/apis/v1/docs/${process.env.CODA_DOC_ID}/tables/${process.env.CODA_TABLE_ID}/rows/${rowId}`;
                const response = await fetch(deleteUrl, {
                  method: 'DELETE',
                  headers: { 'Authorization': 'Bearer ${{ secrets.CODA_TOKEN }}' }
                });
                if (!response.ok) {
                  console.error('❌ Failed to delete Coda entry:', { status: response.status, statusText: response.statusText, responseText: await response.text() });
                  throw new Error(`HTTP error! status: ${response.status}`);
                }
                console.log('✅ Successfully deleted Coda entry');
              } catch (error) {
                console.error('❌ Error deleting Coda entry:', error);
              }
            };

            // Function to get PR labels
            const getPRLabels = async () => {
              const { data: labels } = await github.rest.issues.listLabelsOnIssue({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: prNumber
              });
              return labels;
            };

            // Function to attempt merge
            const attemptMerge = async (sourceBranch, targetBranch) => {
              console.log(`🔄 Attempting merge from ${sourceBranch} to ${targetBranch}...`);
              try {
                const { data: mergeResult } = await github.rest.repos.merge({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  base: targetBranch,
                  head: sourceBranch,
                  commit_message: `chore: merge ${sourceBranch} into ${targetBranch}`
                });
                console.log('✅ Merge successful:', mergeResult.sha);
                return { success: true, sha: mergeResult.sha };
              } catch (error) {
                console.log('❌ Merge failed:', error.message);
                return { success: false, error: error.message };
              }
            };

            // Function to create fallback PR with auto-merge enabled
            const createFallbackPR = async (sourceBranch, targetBranch) => {
              console.log(`🔄 Creating fallback PR from ${sourceBranch} to ${targetBranch}...`);
              try {
                const { data: pr } = await github.rest.pulls.create({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  title: `chore: merge ${sourceBranch} into ${targetBranch}`,
                  head: sourceBranch,
                  base: targetBranch,
                  body: `This PR was automatically created because the automatic merge from ${sourceBranch} to ${targetBranch} failed (likely due to conflicts.)\n\nPlease merge it manually.`
                });

                await github.rest.issues.addLabels({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: pr.number,
                  labels: ['automation:automerge-failed']
                });

                console.log('✅ Fallback PR created:', pr.html_url);

                // Enable auto-merge using GitHub CLI
                console.log('🚀 Enabling auto-merge for PR...');
                try {
                  const { execSync } = require('child_process');
                  const command = `gh pr merge ${pr.number} --auto --merge --repo ${context.repo.owner}/${context.repo.repo}`;
                  execSync(command, {
                    env: {
                      ...process.env,
                      GITHUB_TOKEN: process.env.GH_TOKEN
                    },
                    stdio: 'inherit'
                  });
                  console.log('✅ Auto-merge enabled for PR');
                } catch (autoMergeError) {
                  console.warn('⚠️ Failed to enable auto-merge:', autoMergeError.message);
                  // Don't throw here - the PR was still created successfully
                }

                return pr;
              } catch (error) {
                console.error('❌ Failed to create fallback PR:', error);
                throw error;
              }
            };

            // Main logic for closed PRs
            try {
              const codaQueryUrl = `https://coda.io/apis/v1/docs/${process.env.CODA_DOC_ID}/tables/${process.env.CODA_TABLE_ID}/rows?query=${process.env.CODA_COLUMN_PR_URL}:"${prUrl}"`;
              console.log('📍 Checking Coda for PR entries:', codaQueryUrl);
              const queryResponse = await fetch(codaQueryUrl, { headers: { 'Authorization': 'Bearer ${{ secrets.CODA_TOKEN }}' }});
              if (!queryResponse.ok) throw new Error(`HTTP error! status: ${queryResponse.status}`);
              const existingRows = await queryResponse.json();

              if (existingRows.items && existingRows.items.length > 0) {
                console.log(`🗑️ Found ${existingRows.items.length} Coda entries for this PR`);
                if (!prMerged) {
                  console.log('🔍 PR was closed without merge, deleting Coda entries...');
                  for (const row of existingRows.items) {
                    await deleteCodaEntry(row.id);
                  }
                } else {
                  console.log('✅ PR was merged, traversing through Coda entries to press Deployed button...');
                  for (const row of existingRows.items) {
                    console.log(`🔄 Pressing Deployed button for row ${row.id}...`);
                    const pressUrl = `https://coda.io/apis/v1/docs/${process.env.CODA_DOC_ID}/tables/${process.env.CODA_TABLE_ID}/rows/${row.id}/buttons/${process.env.CODA_BUTTON_DEPLOYED}`;
                    const pressResponse = await fetch(pressUrl, {
                      method: 'POST',
                      headers: { 'Authorization': 'Bearer ${{ secrets.CODA_TOKEN }}' }
                    });
                    if (!pressResponse.ok) throw new Error(`HTTP error pressing button! status: ${pressResponse.status}`);
                    console.log('✅ Successfully pressed Deployed button');
                  }
                }
              } else {
                console.log('ℹ️ No Coda entries found for this PR');
              }
            } catch (error) {
              console.error('❌ Error processing Coda operation for closed PR:', error);
            }

            if (prMerged) {
              console.log('🔍 Checking mergeback conditions for merged PR...');
              const labels = await getPRLabels();
              const hasMergebackLabel = labels.some(label => label.name === 'automation:mergeback');

              if (hasMergebackLabel) {
                let sourceBranch = '';
                let targetBranch = 'main'; // Default target for mergeback

                if (base === 'release/production') sourceBranch = 'release/production';
                else if (base === 'release/qa') sourceBranch = 'release/qa';

                if (sourceBranch) {
                  console.log(`🔄 Starting mergeback process from ${sourceBranch} to ${targetBranch}...`);
                  const mergeResult = await attemptMerge(sourceBranch, targetBranch);
                  if (!mergeResult.success) {
                    console.log('⚠️ Automatic merge failed, creating fallback PR...');
                    try {
                      await createFallbackPR(sourceBranch, targetBranch);
                    } catch (error) {
                      console.error('❌ Failed to create fallback PR during mergeback:', error);
                    }
                  } else {
                    console.log(`✅ Mergeback successful: ${mergeResult.sha}`);
                  }
                } else {
                  console.log(`ℹ️ No mergeback rule for base branch '${base}' or source branch not determined.`);
                }
              } else {
                console.log('ℹ️ PR does not have automation:mergeback label, skipping mergeback.');
              }
            }
            console.log('✅ Closed PR processing completed');
