name: "♻️ | Sync with N<PERSON>"

on:
  workflow_call:

env:
  SOURCE_BRANCH: "release/nxt-production"
  TARGET_BRANCH: "main"
  NXT_MIRRORS: |
    {
      "sensehawk/sample-flask": "nextracker-demo-org/sample-flask"
    }

jobs:
  sync-repos:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source repository
        uses: actions/checkout@v4
        with:
          ref: ${{ env.SOURCE_BRANCH }}
          fetch-depth: 0

      - name: Create Pull Request
        env:
          GITHUB_TOKEN: ${{ secrets.MAINTENANCE_PAT }}
        run: |
          git config user.name 'sensehawk-bot'
          git config user.email '<EMAIL>'

          TARGET_REPO=$(echo '${{ env.NXT_MIRRORS }}' | jq -r '.["${{ github.repository }}"]')
          echo "TARGET_REPO=${TARGET_REPO}" >> $GITHUB_ENV

          git config --unset-all http.https://github.com/.extraheader
          git remote add target https://${GITHUB_TOKEN}@github.com/${TARGET_REPO}.git
          git fetch target ${TARGET_BRANCH}
          SYNC_BRANCH="sync-branch-$(date +%Y%m%d%H%M%S)"
          git checkout -b $SYNC_BRANCH target/${TARGET_BRANCH}
          git merge --allow-unrelated-histories -X theirs origin/${SOURCE_BRANCH}
          git push target $SYNC_BRANCH

          PR_DATE=$(TZ='Etc/UTC' date '+%d %b %Y %I:%M%p')
          SYNC_BRANCH=$(git rev-parse --abbrev-ref HEAD)

          if gh pr create --repo ${TARGET_REPO} \
            --base ${TARGET_BRANCH} \
            --head $SYNC_BRANCH \
            --title "chore: sync [${PR_DATE}]" \
            --body "This PR syncs changes from the ${SOURCE_BRANCH} branch of ${{ github.repository }}."; then
            echo "Pull request created successfully"
          else
            echo "Error: Failed to create pull request. Check if the branch exists and you have necessary permissions."
            exit 1
          fi
