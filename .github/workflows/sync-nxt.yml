name: "♻️ | Sync with N<PERSON>"

on:
  workflow_call:

env:
  SOURCE_BRANCH: "release/nxt-production"
  TARGET_BRANCH: "release/production"
  NXT_MIRRORS: |
    {
      "sensehawk/processing-backend": "sensehawk/nxt-sh-processing-backend",
      "sensehawk/truecapture": "sensehawk/nxt-sh-truecapture",
      "sensehawk/jobs-backend": "sensehawk/nxt-sh-jobprovisioner",
      "sensehawk/core-backend": "sensehawk/nxt-sh-core",
      "sensehawk/qgis-plugin": "sensehawk/nxt-sh-qgis-plugin",
      "sensehawk/mbtiler": "sensehawk/nxt-sh-mbtiler",
      "sensehawk/storage-backend": "sensehawk/nxt-sh-storage",
      "sensehawk/amq-config": "sensehawk/nxt-sh-amq_config",
      "sensehawk/qc-viewer": "sensehawk/nxt-sh-qc-viewer",
      "sensehawk/terra-viewer": "sensehawk/nxt-sh-terra-viewer",
      "sensehawk/zipper": "sensehawk/nxt-sh-zipper",
      "sensehawk/terra-backend": "sensehawk/nxt-sh-terra-backend",
      "sensehawk/storage-service": "sensehawk/nxt-sh-storage-service",
      "sensehawk/auth_middleware": "sensehawk/nxt-sh-auth_middleware",
      "sensehawk/frontend-app-old": "sensehawk/nxt-sh-frontend-app-old",
      "sensehawk/Pix4DEngine": "sensehawk/nxt-sh-Pix4DEngine",
      "sensehawk/storage-backend": "sensehawk/nxt-sh-storage-backend",
      "sensehawk/jobs-backend": "sensehawk/nxt-sh-jobs-backend",
      "sensehawk/auth-middleware-backend": "sensehawk/nxt-sh-auth-middleware-backend",
      "sensehawk/sample-flask": "nextracker-demo-org/sample-flask"
    }

jobs:
  sync-repos:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source repository
        uses: actions/checkout@v4
        with:
          ref: ${{ env.SOURCE_BRANCH }}
          fetch-depth: 0

      - name: Configure Git
        run: |
          git config user.name 'sensehawk-bot'
          git config user.email '<EMAIL>'

      - name: Extract target repo
        id: extract-repo
        run: |
          TARGET_REPO=$(echo '${{ env.NXT_MIRRORS }}' | jq -r '.["${{ github.repository }}"]')
          echo "TARGET_REPO=${TARGET_REPO}" >> $GITHUB_ENV

      - name: Force push source branch to target repo
        id: push
        continue-on-error: true
        env:
          GITHUB_TOKEN: ${{ secrets.MAINTENANCE_PAT }}
        run: |
          git config --unset-all http.https://github.com/.extraheader
          git remote add target https://${GITHUB_TOKEN}@github.com/${TARGET_REPO}.git
          git push --force target ${SOURCE_BRANCH}:${TARGET_BRANCH}

      - name: Add summary
        if: always()
        run: |
          STATUS="${{ steps.push.outcome }}"
          echo "## 🔁 Sync Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Source Repository:** \`${{ github.repository }}\`" >> $GITHUB_STEP_SUMMARY
          echo "**Source Branch:** \`${{ env.SOURCE_BRANCH }}\`" >> $GITHUB_STEP_SUMMARY
          echo "**Target Repository:** \`${{ env.TARGET_REPO }}\`" >> $GITHUB_STEP_SUMMARY
          echo "**Target Branch:** \`${{ env.TARGET_BRANCH }}\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          if [ "${STATUS}" = "success" ]; then
            echo "✅ The contents of the source branch were successfully **force-pushed** to the target branch." >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ **Sync failed**: An error occurred while force-pushing to the target repository." >> $GITHUB_STEP_SUMMARY
          fi
